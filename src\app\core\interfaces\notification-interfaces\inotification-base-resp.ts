import { NotificationsModuleEnum } from "../../enums/notifications-module-enum.enum";
import { ISystemCallNotification } from "../calls/interview/isystem-call-notification";
import { IFreeRicitiationCallNotifi } from "./ifree-ricitiation-call-notifi";
import { StartCallGroupExplaNotif } from "./start-call-group-expla-notif";
import { StartCallNotif } from "./start-call-notif";

export interface INotificationBaseResp {
    // title:string;
    // body:string;
    notification: IFirebaseNotificationNotifiObj;
    data: IFirebaseNotificationDataObj;
    notifBodyModel: INotificationRespBody;
}

export interface IFirebaseNotificationDataObj {
    details: string;
}

export interface IFirebaseNotificationNotifiObj {
    title: string;
    body: string;
}


export interface INotificationRespBody {
    id: string;
    modeType: NotificationsModuleEnum;
    modTypeId: string;
    arModeTypeName: string;
    enModeTypeName: string;
    title: string;
    titleAr: string;
    titleEn: string;
    bodyAr: string;
    bodyEn: string;
    body: string;
    credOn: string;
    details: string;
}

export interface INotificationTasmeeaTaskRespBody extends INotificationRespBody {
    tasmeeaDetailsModel: StartCallNotif
}

export interface INotificationSardTolabRespBody extends INotificationRespBody {
    sardTolabDetailsModel: StartCallNotif
}

export interface INotificationGroupExplanationRespBody extends INotificationRespBody {
    groupExplanationDetailsModel: StartCallGroupExplaNotif
}

export interface INotificationFreeRecitationRespBody extends INotificationRespBody {
    freeRecitationDetailsModel: IFreeRicitiationCallNotifi
}
export interface INotificationInterviewRespBody extends INotificationRespBody {
    interviewNotificationModel: ISystemCallNotification
}
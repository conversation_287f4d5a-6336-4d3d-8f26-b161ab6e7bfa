        export interface IProgramDayTasksModel {
            id?:string;
            detailsTask?:string;
            dayTask?:string;
            dayTaskNameAr?:string;
            dayTaskNameEn?:string;
            programDutyDay?:string;
            order?:number;
            huffazTask?:number;
            dutyDay?:string;
            answered?:boolean; // use to student
        }

    


   


   
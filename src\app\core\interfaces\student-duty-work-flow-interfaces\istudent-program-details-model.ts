import {
  IProgramRecitationTimesDetails,
  IProgramStudentWeeklyDutiesDaysDetails,
  IProgramTypesDetails
} from '../programs-interfaces/iprogram-details';

export interface IStudentProgramDetailsModel {
id?:string;
huffno?:number;
prgName?:string;
prgAllowDutyDays?:string;
prgAdvan?:string;
prgAvailaDutyTime?:string;
prgDura?:number;
prgGoal?:string;
prgIda?:string;
prgIsConExa?:boolean;
prgIsPassExaRequ?:boolean;
prgIsPubli?:boolean;
prgIsRecitRequ?:boolean;
prgIsRecitTimeMand?:boolean;
prgIsSard?:boolean;
prgMeth?:string;
prgNoDutyDays?:number;
prgPasuDate?:string;
prgPledgTxt?:string;
prgPubliDate?:string;
prgVisi?:string;
prgSharType?:string;
prgArSharTypeName?:string;
prgEnSharTypeName?:string;
prgDutiDayType?:string;
prgArDutiDayTypeName?:string;
prgEnDutiDayTypeName?:string;
prgRecitType?:string;
prgArRecitTypeName?:string;
prgEnRecitTypeName?:string;
batId?:string;
arBatName?:string;
enBatName?:string;
batEnSubsDat?:string;
batStaSubsDat?:string;
studStarDate?:string;
prgTps?:IProgramTypesDetails[];
prgWeekDutiDas?:IProgramStudentWeeklyDutiesDaysDetails[];
prgRecitTms?:IProgramRecitationTimesDetails[];
}

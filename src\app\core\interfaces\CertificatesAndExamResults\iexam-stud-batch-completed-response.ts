import { IQuestion } from "../exam-builder-interfaces/iquestion";

export interface IExamStudBatchCompletedResponse {
    studTaskId?: string,
    answ?: string,
    answerModel?: IQuestion[],
    isAbs?: boolean,
    isAnsw?: boolean,
    isPass?: boolean,
    studGrad?: number,
    gradTask?: number,
    taskTyp?: number,
    order?: number,
    subTaskDate?: Date,
    progName?: string,
    arBatName?: string,
    enBatName?: string,
    arProgBatName?: string,
    enProgBatName?: string,

}

import { Directive, Input } from '@angular/core';
import { IGetStudAgenda } from '../interfaces/teacher-statistic-interfaces/iget-stud-agenda';

@Directive({
  selector: '[appStudAgendaTasmeaTask]',
  exportAs:'appStudAgendaTasmeaTask'
})
export class StudAgendaTasmeaTaskDirective {

  @Input() studAgenda: IGetStudAgenda | undefined;

  constructor() { }

 ngOnInit() {
   let answerModel=JSON.parse(this.studAgenda?.answ || "{}"); 

   if (this.studAgenda && answerModel && answerModel.length > 0){
     this.studAgenda.answerModel = [];
     
     answerModel.forEach((item:string) => {
       this.studAgenda?.answerModel?.push(JSON.parse(item || "{}"));
      });
    }
  }
}

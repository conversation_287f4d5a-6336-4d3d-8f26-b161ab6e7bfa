export interface IScientificProblemGridItems {
    id?:string;
    scNo?:number;
    questText?:string;
    repText?:string;
    studNameEn?:string;
    studNameAr?:string;
    adNameEn?:string;
    adNameAr?:string;
    scCreatedOn?:string;
    avatarLink?:string;
    IsQuesBank?:boolean;
    checked?:boolean;
    progId?:string;
    batId?:string;
    progName?:string;
    arBatName?:string;
    enBatName?:string;
    arProgBatchName?:string;
    enProgBatchName?:string;
    arProgramTaskName?:string;
    enProgramTaskName?:string;
    dayNum?:number;
}

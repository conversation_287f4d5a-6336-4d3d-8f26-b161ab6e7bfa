export interface IScientificMaterialGrid{
    id?:string;
    huffazId?:number;
    categoryNumber?:number; 
    matrialTitleAr?:string;
    matrialTitleEn?:string;
    categoryId?:string;
    matrialCategoryAr?:string;
    matrialCategoryEn?:string;
    fileLink?:string;
    isActive?:boolean;
    availableForAllUsers?:boolean;
    matrialProgramsNamesAr?:string;
    matrialProgramsNamesEn?:string;
    urlFile?:string;
    totalRows?:number;    
}
import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router } from '@angular/router';
import { IUser } from 'src/app/core/interfaces/auth-interfaces/iuser-model';
import { RoleManagementService } from 'src/app/core/services/role-management/role-management.service';


@Injectable({
  providedIn: 'root'
})
export class DashboardGuard implements CanActivate {
  constructor(private router: Router, private roleService: RoleManagementService) { }
  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): boolean {

    var user = JSON.parse(localStorage.getItem("user") || '{}') as IUser;

    let url = state.url;
//fix buggs https://moddaker.atlassian.net/browse/HOF-4733
    if (user.token && (!this.roleService.isTeacher()&& !this.roleService.isStudent()) && url === '/dashboard') {
      return true;
    }
    else if (user.token && this.roleService.isTeacher() && url === '/dashboard/teacher-dashboard') {
      return true;
    }
    else if (user.token && this.roleService.isStudent() && url === '/dashboard/student-dashboard') {
      return true;
    }

    this.router.navigateByUrl('not-authorized');
    return false;
  }

}


import { IprogramStudentDetails } from "./iprogram-student-details";
// import { IProgramTasksModel } from "./iprogram-tasks-model"
// import { IStudentProgramDutyTasks } from "./istudent-program-duty-tasks";

export interface IStudentStatisticsResponse {
    //ساعات السرد لسه متعملتسش
    programDutyDaysCount?: number,//مقدار إنجاز الطالب بالأيام
    studentProgramDutyDaysPercentagePerDays?: number,
    programDetailsModel: IprogramStudentDetails // men gwaa // duration
    expectedEndDate?: Date,//تاريخ الانتهاء المتوقع
    studentDayTasksPercentage?: number, //إنجاز الطالب مهامه  اليومية
    dailyTestCount?: number, //الاختبارات اليوميه المنجزة
    phaseTestCount?: number,//الاختبارات المرحلية المنجزة
    phaseTestPercentage?: number, //نسبة الاختبارات المرحلية
    tasmeeaCount?: number,//عدد حلقات السرد
    // programTasksModel?: IProgramTasksModel[], // men gwaa // اوحه المحفوظه
    // studentProgramDutyTasksModel?: IStudentProgramDutyTasks[] //not used

    memorizeTaskAttachmentCount?: number,
    repetitionTaskAttachmentCount?: number,
    linkingTaskAttachmentCount?: number,
    reviewTaskAttachmentCount?: number,
    explanationTaskAttachmentCount?: number,
  actualRecitationHours?:string;
  readExplanationTaskAttachmentCount?:number;
}

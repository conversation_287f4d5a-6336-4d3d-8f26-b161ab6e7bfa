import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router } from '@angular/router';
import { IUser } from 'src/app/core/interfaces/auth-interfaces/iuser-model';
import { RoleManagementService } from 'src/app/core/services/role-management/role-management.service';


@Injectable({
  providedIn: 'root'
})
export class QuestionBankGuard implements CanActivate {
  constructor(private router: Router, private roleService: RoleManagementService) { }

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): boolean {

    var user = JSON.parse(localStorage.getItem("user") || '{}') as IUser;

    let url = state.url;

    if (user.token && url === '/question-bank/question-bank-view'
      //  && (this.roleService.isAdmin() || this.roleService.isTeacher() || this.roleService.isStudent())
    ) {
      return true;
    }


    this.router.navigateByUrl('not-authorized');
    return false;
  }
}
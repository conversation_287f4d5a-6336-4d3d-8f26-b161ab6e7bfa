import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { IUser } from 'src/app/core/interfaces/auth-interfaces/iuser-model';
import { RoleManagementService } from 'src/app/core/services/role-management/role-management.service';


@Injectable({
  providedIn: 'root'
})
export class AdminStudentGuard implements CanActivate {
  constructor(private router: Router, private roleService: RoleManagementService) { }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {

    var user = JSON.parse(localStorage.getItem("user") || '{}') as IUser;

    if (user.token && (!this.roleService.isTeacher()&& !this.roleService.isStudent())) {
      return true;
    }
    this.router.navigateByUrl('not-authorized');
    return false;
  }

}

export interface IStudentSubscriptionModel {
    id?: string;
    usrId?: string;
    requestDate?: string;
    usrNameAr?: string;
    usrNameEn?: string;
    progName?: string;
    totalRows?: number;
    checked?: boolean;
    // programStaNum?: number;
    examDegree?: number;
    avatarLink?: string;
    // reasonReject?: string;
    arBatName?: string;
    enBatName?: string;
    arProgBatName?: string;
    enProgBatName?: string;
    statNum?: number;
    arStatName?: string;
    enStatName?: string;
    no?: number;
    rejReas?: string;
    reasonReject?: string;
    stdGrade?: number;
    totalScore?: number;
    isProgConExa?: boolean;
}

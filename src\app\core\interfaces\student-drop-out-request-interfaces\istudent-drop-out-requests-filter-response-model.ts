export interface IStudentDropOutRequestsFilterResponseModel {
    id?: string;
    usrId?: string;
    requestDate?: string;
    usrNameAr?: string;
    usrNameEn?: string;
    progName?: string;
    batNameAr?: string;
    batNameEn?: string;
    arProgBatchName?: string;
    enProgBatchName?: string;
    totalRows?: number;
    checked?: boolean;
    reasonReject?: string;
    // rejReas?:string;
    avatarLink?: string;
    reason?: string;
    no?: number;
}

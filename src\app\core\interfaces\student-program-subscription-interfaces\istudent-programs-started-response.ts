import { Time } from '@angular/common';
export interface IstudentProgramsStartedResponse {

    id?: string;
    batId?: string;
    progName?: string;
    arBatName?: string;
    enBatName?: string;
    arProgBatchName?: string;
    enProgBatchName?: string;
    isDaysRequested?: boolean;
    isProgStarted?: boolean;
    noofDutyDays?: number;
    remainingTime?: Time;
    remainingTimeInNumbers?: number;

}


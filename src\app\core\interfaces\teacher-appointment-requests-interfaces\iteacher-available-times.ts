
export interface ITeacherAvailableTimes {
  isView? : boolean;
  teacherProfileAvailableTimes?:ITeacherProfileAvailableTimes [];
  teacherAvailableTimes?: ITeacherAvailableTimesResponseModel[];
}

export interface ITeacherProfileAvailableTimes {
  teacherAvailableTimes?: ITeacherAvailableTimesResponseModel[];
  availableDay?:IDayModel;
}

export interface ITeacherAvailableTimesResponseModel {
  idAvailableDay?: string;
  id?: string;
  timeFrom?: string;
  timeTo?: string;
  dayAr?: string;
  dayEn?: string;

}



export interface ITeacherAppointmentRequestsAppointmentsDetails {
  teacherTimesModels?:ITeacherTimesModel [];
  reqId?: string;
}

export interface ITeacherTimesModel {
  appointmentRequestDetailsList?: ITeacherAppointmentRequestDetails [];
  availableDay?:IDayModel;

}
export interface IDayModel {
  id?: string;
  nameAr?: string;
  nameEn?: string;
}
export interface ITeacherAppointmentRequestDetails {
  id?: string;
  no?: string;
  from?: string;
  to?: string;
  dayAr?: string;
  dayEn?: string;
}

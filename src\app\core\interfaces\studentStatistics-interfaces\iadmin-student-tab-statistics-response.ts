interface IRegisteredStudentsModel {
  registeredStdCount?:number;
}

interface IStudentDropOutModel {
  dropOutStdCount?:number;
}

interface IStudentProgVacationModel {
  vacationStdCount?:number;
}

export interface IAdminStudentTabStatisticsResponse {
  registeredStdModel?:IRegisteredStudentsModel;
  dropOutStdModel?:IStudentDropOutModel;
  vacationStdModel?:IStudentProgVacationModel;
  discontinuedStdCount?:number;
  fallBehindStdCount?:number;
  continuingStdCount?:number;
}

import { Injectable } from '@angular/core';
import {
  Http<PERSON>e<PERSON>,
  <PERSON>ttpH<PERSON><PERSON>,
  HttpEvent,
  HttpInterceptor
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from "rxjs/operators";
import { Router } from '@angular/router';
import { AlertifyService } from '../../services/alertify-services/alertify.service';
import { TranslateService } from '@ngx-translate/core';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {

  constructor(private router: Router, 
    private alertify : AlertifyService,
    private translate:TranslateService) {}

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      catchError((err) => {
        if (err.status === 401 /*|| err.status === 403*/) {
          // auto logout if 401 response returned from api
          //this.router.navigate(["/notAuth"]);
          this.alertify.error(this.translate.instant('GENERAL.AUTHENTICATION_REQUIRED'));
        }

        if (err.status === 403){
          // alert('unauthorized, please contact your adminstrator');
          // this.router.navigate(['/dashboard'])
          this.router.navigateByUrl('not-authorized');
        }

        // if (err.status === 0) {
        //   this.alertify.error(this.translate.instant('GENERAL.SOMETHING_WENT_WRONG'));
        // }

        const error = err.error.message || err.statusText;
        return throwError(error);
      })
    );
  }
}

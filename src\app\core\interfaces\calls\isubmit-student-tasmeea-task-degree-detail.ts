import { IAttachment } from "../attachments-interfaces/iattachment";
import { IProgramDayTasksModel } from "../programs-interfaces/iprogram-day-tasks-model";

export interface ISubmitStudentTasmeeaTaskDegreeDetail {
    nameAr?:string;
    nameEn?:string;
    bookAttatchments?:IAttachment[];
    Task?:IProgramDayTasksModel;
    stu?:IStudentTasmeeaTaskDegreeDetails;
}

export interface IStudentTasmeeaTaskDegreeDetails {
    fromUsrId?:string;
    fromNameEn?:string;
    fromNameAr?:string;
    fromUserAvatar?:string;  
}

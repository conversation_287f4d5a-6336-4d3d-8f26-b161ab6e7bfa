export interface IProgramBasicInfoDetailsModel {
    progId?: string;
    basicId?: string;
    progName?: string;
    shareWith?: string;
    progType?: string;
    durationProg?: string;
    dutyTime?: string;
    availableDuty?: string;
    ideaProg?: string;
    goalProg?: string;
    visionProg?: string;
    pathProg?: string;
    advantageProg?: string;
    textPledge?: string;
    dutiesDayType?: string;

    examPass?: string;
    rateProg?: string;
    rectMand?: string;
    isAlsard?: string;
    recitType?:string;
    // recitationPeriod?: string;
    // fromTime?: string;
    // toTime?: string;
    // numberOfDays?: string;
    // specificDays?: string;
}

import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import jwt_decode, { JwtPayload } from 'jwt-decode';

@Injectable({
  providedIn: 'root'
})
export class PermissionServiceService {

  constructor(private http: HttpClient) { }
  localUser?: null;
  getUserScopes(): Array<string> {
    var localUser = localStorage.getItem("user");
    if (localUser) {

      var user = JSON.parse(localUser);
      var token = user.token;
      var decoded = jwt_decode<any>(token || '') || null;
      return decoded['scope'].split(" ");
    }
    else
      return [];

  }

  //// Question Bank Permissions
  canAddQuestionBankCategory() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'add-qb-catg:catg').length > 0;
  }
  canViewQuestionBankCategoryDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-qb-catg-details:catg').length > 0;
  }
  canEditQuestionBankCategory() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'edit-qb-catg:catg').length > 0;
  }

  canDeleteQuestionBankCategory() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'delete-qb-catg:catg').length > 0;
  }
  canViewQuestionBankCategory() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-qb-catg:catg').length > 0;
  }
  canAddQuestionBankQuestion() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'add-qb-ques:ques').length > 0;
  }
  canViewQuestionBankQuestionDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-qb-ques-details:ques').length > 0;
  }
  canEditQuestionBankQuestion() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'edit-qb-ques:ques').length > 0;
  }
  canDeleteQuestionBankQuestion() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'delete-qb-ques:ques').length > 0;
  }

  canViewQuestionBankQuestion() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-qb-ques:ques').length > 0;
  }
  canUpdateQuestionBankQuestionOrder() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'order-qb-ques:ques').length > 0;
  }

  //Scientific Material
  canAddScientificMaterial() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'add-scm:scm').length > 0;
  }
  canViewScientificMaterialDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-scm-details:scm').length > 0;
  }

  canEditScientificMaterial() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'edit-scm:scm').length > 0;
  }

  canDeleteScientificMaterial() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'delete-scm:scm').length > 0;
  }

  canViewScientificMaterial() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-scm-s:scm').length > 0;
  }

  canViewScientificMaterialCategory() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-scm-catg:scm').length > 0;
  }
  canAddScientificProblems() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'add-scp:scp').length > 0;
  }
  canViewScientificProblems() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-scp-details:scp').length > 0;
  }
  canDeleteScientificProblems() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'delete-scp:scp').length > 0;
  }
  canAddScientificReply() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'add-scr:scp').length > 0;
  }

  /// Walk Throughs

  canAddWalkThrough() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'add-wt:wt').length > 0;
  }
  canViewWalkThroughDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-wt-details:wt').length > 0;
  }
  canEditWalkThrough() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'edit-wt:wt').length > 0;
  }
  canDeleteWalkThrough() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'delete-wt:wt').length > 0;
  }

  canViewWalkThroughs() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-wt-s:wt').length > 0;
  }
  ///Look up

  canViewLookUps() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-lucs:lucs').length > 0;
  }
  canViewCitiesByCountryId() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-lucs-city:lucs').length > 0;
  }

  //Feeling
  canViewFeelings() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-fels:fels').length > 0;
  }
  canAddFeeling() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'add-fels:fels').length > 0;
  }
  canAproveFeeling() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'aprove-fels:fels').length > 0;
  }

  //Content Management System
  canViewContentManagementSystems() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-cms:cms').length > 0;
  }
  canViewContentManagementSystemsDetailsByType() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-cms-by-type:cms').length > 0;
  }
  canViewContentManagementSystemsDetailsById() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-cms-by-id:cms').length > 0;
  }
  canAddContentManagementSystem() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'add-cms:cms').length > 0;
  }
  canEditContentManagementSystem() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'edit-cms:cms').length > 0;
  }
  canDeleteContentManagementSystem() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'delete-cms:cms').length > 0;
  }
  //Attachments
  canUploadAttachments() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'upload-att:att').length > 0;
  }
  canDownloadAttachments() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'download-att:att').length > 0;
  }
  canDeleteAttachments() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'delete-att:att').length > 0;
  }

  // module ex
  canAddExamForm() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-ex:exfrm').length > 0;
  }
  canUpdateExamForm() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'put-ex-att:exfrm').length > 0;
  }
  canDeleteExamForm() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-ex:exfrm').length > 0;
  }
  canViewExamFroms() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-ex:exfrm').length > 0;
  }
  canViewExamFormDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-ex-det:exfrm').length > 0;
  }
  // module 

  canViewHonorboardAdminDashboard() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-adb-hon-bord:adb').length > 0;
  }
  canViewStudentStatisticsAdminDashboard() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-adb-stc:adb').length > 0;
  }
  canViewStudentTotalExamTaskDegreeAdminDashboard() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-adb-tot-ex-deg:adb').length > 0;
  }

  canViewStudentTotalTaskDegreeAdminDashboard() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-adb-tot-tas-deg:adb').length > 0;
  }

  canViewTaskCountAdminDashboard() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-adb-tas-con:adb').length > 0;
  }

  canViewTasmeeaCountAdminDashboard() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-adb-tasm-con:adb').length > 0;
  }

  canViewProgramScientificProblemAdminDashboard() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-adb-mat-con-pro:adb').length > 0;
  }
  // module  prog

  canAddProgram() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-prog:prog').length > 0;
  }
  canEditProgram() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ed-prog:prog').length > 0;
  }
  canFilterProgramByName() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'filt-nm-prog:prog').length > 0;
  }
  canFilterProgramAdvancedFilter() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'adv-filt-prog:prog').length > 0;
  }

  canDeleteProgram() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-prog:prog').length > 0;
  }
  canPublishPauseProgram() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'pub-pau-prog:prog').length > 0;
  }
  canAddProgramDayTasks() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-prog-dy-tsk:prog').length > 0;
  }

  canSaveProgramDayTaskDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'sv-prog-dy-tsk-det:prog').length > 0;
  }

  canCopyProgramDayTask() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'cop-prog-dy-tsk:prog').length > 0;
  }
  canDeleteProgramDayTask() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-prog-dy-tsk:prog').length > 0;
  }

  canViewPorgramDayTasks() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tsk-prog-dty-dy:prog').length > 0;
  }

  canUpdateDayTasksOrder() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ed-ord-by-prog-dy-tsk:prog').length > 0;
  }

  canAddProgramExamForm() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-emfm-prog:prog').length > 0;
  }

  canEditProgramExamForm() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ed-em-prog:prog').length > 0;
  }
  canCopyProgramDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'cpy-prog:prog').length > 0;
  }
  canAddProgramNotification() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-prog-notf:prog').length > 0;
  }


  canEditProgramNotification() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ed-prog-notf:prog').length > 0;
  }

  canDeleteProgramNotification() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-prog-notf:prog').length > 0;
  }

  canViewPorgramNotification() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-prog-notfs:prog').length > 0;
  }

  canViewPorgramDetials() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'view-det-prog:prog').length > 0;
  }

  canAssignConditionsToPorgram() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ass-cond-prog:prog').length > 0;
  }

  canListLastFiveMemorizeTasksPerProgramDuties() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'lst-fv-mem-tsks-prog:prog').length > 0;
  }

  canViewAssignedProgramConditions() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-ass-prog-cond:prog').length > 0;
  }

  canEditProgramConditionDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ed-prog-cond-det:prog').length > 0;
  }

  canDeleteProgramCondition() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-prog-cond:prog').length > 0;
  }

  canViewConitionsNotAssignedToProgram() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-cond-no-ass-prog:prog').length > 0;
  }

  canViewPorgramDetailsForSubscription() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-prog-subs-det:prog').length > 0;
  }

  canViewSharedPorgrams() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-shrd-progs:prog').length > 0;
  }

  canViewPorgramNotDeletedNotPublished() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-pub-no-del-progs:prog').length > 0;
  }

  canViewProgramMemorizedTasksPerDay() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-prog-mem-tsks-dy:prog').length > 0;
  }

  canViewProgramDays() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-prog-dys:prog').length > 0;
  }

  canViewAllPrograms() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-all-progs:prog').length > 0;
  }
  // module

  canAddPredefinedConditions() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-ppcc').length > 0;
  }

  canViewPredefinedConditionsDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-ppcc-det:ppcc').length > 0;
  }

  canEditPredefinedConditions() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ed-ppcc:ppcc').length > 0;
  }


  canDeletePredefinedCondition() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-ppcc:ppcc').length > 0;
  }


  canViewAllPredefinedConditions() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-all-ppcc:ppcc').length > 0;
  }
  // module

  canStartCall() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'st-cl:cls').length > 0;
  }

  canJoinCall() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'jo-cl:cls').length > 0;
  }

  canEndCall() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'en-cl:cls').length > 0;
  }

  canStartGroupExplanationCall() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'st-cl-gr:cls').length > 0;
  }

  canJoinGroupExplanationCall() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'jo-cl-gr:cls').length > 0;
  }


  canEndGroupExplanationCall() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'en-cl-gr:cls').length > 0;
  }

  canRateUserAfterCall() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'rt-usaf-cl:cls').length > 0;
  }

  canViewCallUsersInformationForRating() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-usr-inf-rt:cls').length > 0;
  }

  canViewUserTaskTasmeeaDetailsForDegree() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-usr-tas-tsk-deg-det:cls').length > 0;
  }


  canJoinSystemInterviewCall() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'sys-inv-jon-cal:cls').length > 0;
  }

  canEndSystemInterviewCall() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'sys-inv-end-cal:cls').length > 0;
  }

  canViewAvailableTeacherForFreeRecitaionCall() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-ava-tech-fr-rec:cls').length > 0;
  }
  canAddFreeRecitationAppointmentRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-fr-rec-app:cls').length > 0;
  }
  canViewStudentAppointmentsForFreeRecitation() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-app-fr-rec:cls').length > 0;
  }

  canViewTeacherFreeRecitationAppointmentRequests() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tech-fr-rec-app-req:cls').length > 0;
  }

  canAcceptStudentFreeRecitationRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'acp-std-app-req:cls').length > 0;
  }

  canRejectStudentFreeReciationRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'rej-std-app-req:cls').length > 0;
  }

  canStudentCancelFreeReciationRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'std-can-app-req:cls').length > 0;
  }

  canViewFreeRecitationRequestDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-fr-rec-req-det:cls').length > 0;
  }

  canViewAvailableTeachersForProgramTasksRecitation() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-ava-tchs:cls').length > 0;
  }

  canViewAvailableStudentsForProgramTasksRecitation() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-ava-stds:cls').length > 0;
  }

  canSubmitTaskAfterCall() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'sub-tsk-afr-cal:cls').length > 0;
  }

  canEditProgramRecitationTaskDegree() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'pu-tsk-deg:cls').length > 0;
  }

  canViewUsersListToAddIntoGroupExplanation() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-usrs-ad-in-grp-exp:cls').length > 0;
  }

  canAddGroupExplanation() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-grp-exp:cls').length > 0;
  }

  canAddGroupExplanationMembers() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'add-grp-mem-grp-exp:cls').length > 0;
  }
  canStudentApplyRequestToJoinGroupExplanation() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'std-req-jon-grp-exp:cls').length > 0;
  }
  canAcceptGroupExplanationJoinRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'acp-grp-exp-req:cls').length > 0;
  }
  canRejectGroupExplanationJoinRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'rej-grp-exp-req:cls').length > 0;
  }
  canStudentCancelGroupExplanationJoinRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'std-can-grp-exp-req:cls').length > 0;
  }

  canViewGroupExplanationDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-grp-exp-det:cls').length > 0;
  }

  canViewGroupExplanationMemebersMobile() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-grEx-mem:cls').length > 0;
  }

  canViewGroupExplanationJoinRequests() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-grEx-joRq:cls').length > 0;
  }
  canViewGroupExplanationTeacherView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-grp-exp-tech-mod:cls').length > 0;
  }

  canViewGroupExplanationStudentView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-grp-exp-std-mod').length > 0;
  }
  canViewAllGroupsExplanation() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-grps-exp:cls').length > 0;
  }
  canViewGroupsExplanationNotBelongToUser() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-grps-exp-no-bln-usr:cls').length > 0;
  }

  canViewJoinGroupExplanationRequests() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-jon-grps-exp-reqs:cls').length > 0;
  }

  canDeleteStudentFromGroupExplanation() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-std-fr-grp-exp:cls').length > 0;
  }

  // module

  canViewStudentBatchesCompletedStatistics() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-bat-com:cer').length > 0;
  }

  canViewStudentBatchesExamCompletedStatistics() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-exm-std-bat-com:cer').length > 0;
  }

  canViewStudentBatchesNotCompletedStatistics() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-bat-no-com:cer').length > 0;
  }
  canViewStudentBatchesExamNotCompletedStatistics() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-exm-std-bat-no-com:cer').length > 0;
  }
  canViewProgramBatchHonorBoard() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-prog-bat-hon-brd:cer').length > 0;
  }

  canStartSystemInterviewCall() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'sys-inv-srt-cal').length > 0;
  }

  canPrintCertificate() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'pr-cr:cer').length > 0;
  }

  // module

  canAddProgramBatch() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-prbt:prbt').length > 0;
  }

  canUpdateProgramBatch() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'up-prbt:prbt').length > 0;
  }

  canViewProgramBatcheDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-prbt:prbt').length > 0;
  }

  canDeletePorgramBatch() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-prbt-bbtid:prbt').length > 0;
  }
  canViewProgramBatchTeachersAndStudents() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tchanstu-inprbt:prbt').length > 0;
  }
  canViewProgramBatches() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-prbt:prbt').length > 0;
  }
  // module

  canContactUs() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'sd-ma:cnts').length > 0;
  }
  // module

  canAddCorruptedFiles() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-crp-fi:crpf').length > 0;
  }
  canViewCorruptedFilesRequests() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-crp-fis-reqs:crpf').length > 0;
  }

  canDeleteListOFCorruptedFilesRequests() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-lst-crp-fis-reqs:crpf').length > 0;
  }
  canDeleteCorruptedFileRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-crp-fi-req:crpf').length > 0;
  }

  canEditCorruptedFileRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ed-crp-fi-req:crpf').length > 0;
  }

  // module
  canSendNotifications() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-nt:nt').length > 0;
  }
  // module

  canAddProgramCategory() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-prog-ctg:prgctg').length > 0;
  }
  canEditProgramCategory() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ed-prog-ctg:prgctg').length > 0;
  }

  canDeleteProgramCategory() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-prog-ctg:prgctg').length > 0;
  }
  canViewPorgramCategoryDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-prog-ctg-det:prgctg').length > 0;
  }

  canViewProgramCategories() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-prog-ctgs:prgctg').length > 0;
  }
  canViewCategoryPrograms() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-ctg-progs:prgctg').length > 0;
  }
  // module
  canRegisterNotificationToken() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'reg-not-tkn:pshnt').length > 0;
  }
  canViewUserNotificationsHistoricalList() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-usr-not:pshnt').length > 0;
  }
  // module

  canAddNewRole() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-rl:rles').length > 0;
  }
  canEditRole() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ed-rl:rles').length > 0;
  }
  canViewRoleDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-rl-det:rles').length > 0;
  }

  canViewRoles() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-rls:rles').length > 0;
  }

  canDeleteRole() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-rl:rles').length > 0;
  }
  canassignRolePermissions() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'asgn-rl-prm:rles').length > 0;
  }
  canAssignRoleUsers() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'asgn-rl-usrs:rles').length > 0;
  }


  canViewUserRolesPermissions() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-usr-rls-perms:rles').length > 0;
  }
  canViewPermissionsTree() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-perms-tree:rles').length > 0;
  }

  canViewUsersListNotBelongToRole() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-usrs-no-bng-rl:rles').length > 0;
  }

  canViewUsersExceptStudents() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-usrs-exc-stds:rles').length > 0;
  }

  canViewUsersPerRole() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-usrs-b-rl:rles').length > 0;
  }
  canViewRolesList() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-rls-lst:rles').length > 0;
  }

  // module

  canAddStudentDropoutRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-std-drpo-rqs:stdrpo').length > 0;
  }

  canViewStudentDropoutRequestsFilterAdminView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-drpo-req-adm-vfilt:stdrpo').length > 0;
  }
  canViewStudentDropoutRequestsFilterStudentView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-drpo-req-std-vfilt:stdrpo').length > 0;
  }


  canAcceptStudentDropoutRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ac-std-drpo-req:stdrpo').length > 0;
  }
  canRejectStudentDropoutRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'rej-std-drpo-req:stdrpo').length > 0;
  }

  canStudentCancelDropoutRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'std-can-drpo-req:stdrpo').length > 0;
  }
  canViewAvailableProgramsListForDropoutRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-ava-prog-lst-std-drpo:stdrpo').length > 0;
  }

  // module


  canViewStudentProgramDutyDays() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-prog-du-dys:stddwf').length > 0;
  }

  canSubmitStudentTasksAnswers() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'sb-std-tsk-anw:stddwf').length > 0;
  }

  canStartStudentBatchWorkflow() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'str-std-bat:stddwf').length > 0;
  }
  canViewDayTasksByStudentProgBatchDutyDayTask() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-dy-tsk-b-std-prog-dy:stddwf').length > 0;
  }


  canViewStudentProgramDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-prog-det:stddwf').length > 0;
  }


  canCompleteStudentBatch() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'com-std-bat:stddwf').length > 0;
  }
  // module

  canViewStudentsAdminViewStudentTab() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-stds:stdmg').length > 0;
  }
  canAddStudentToSharedProgramAdminViewStudentTab() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-std-t-shrd-prog:stdmg').length > 0;
  }
  canViewStudentProgramsAdminViewStudentTab() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-progs:stdmg').length > 0;
  }
  canViewStudentProgBatchDaysAdminViewStudentTab() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-prog-bat-dys:stdmg').length > 0;
  }
  canViewStudentProgBatchDaysTasksAdminViewStudentTab() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-prog-bat-tsk:stdmg').length > 0;
  }

  canDeleteStudentAdminViewStudentTab() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-std:stdmg').length > 0;
  }

  // module

  canViewStudentProgSubscriptionsFilterAdminView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-sub-filt-adm-sec:spgsu').length > 0;
  }
  canViewProgramsSubscriptionsRequestFilterStudentView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-prgs-sub-fit-std-sec:spgsu').length > 0;
  }
  canAcceptStudentProgramSubscription() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ac-std-prg-sub:spgsu').length > 0;
  }

  canRejectStudentProgramSubscription() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'rej-std-prg-sub:spgsu').length > 0;
  }
  canStudentViewProgramCustomConditions() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-cst-con:spgsu').length > 0;
  }
  canStudentSubmitProgramCustomConditions() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'sub-std-cst-con-anw:spgsu').length > 0;
  }

  canStudentViewProgramRandomExam() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-prg-ran-ex:spgsu').length > 0;
  }

  canStudentSubmitProgramRandomExam() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'sub-std-jon-ex-anw:spgsu').length > 0;
  }

  canStudentCompleteApplyingSubscriptionRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'std-prg-com-sub:spgsu').length > 0;
  }
  canStudentViewProgramsForSubscriptions() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-prg-std-sub:spgsu').length > 0;
  }

  canStudentVerifyProgramPredefinedConditions() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vfy-prg-prd-con:spgsu').length > 0;
  }
  canViewStudentPrograms() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-prgs:spgsu').length > 0;
  }

  canViewStudentStartedPrograms() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-prgs-strd:spgsu').length > 0;
  }
  canViewExamAnswer() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-ex-anw:spgsu').length > 0;
  }
  // module 

  canViewStudentVacationsRequestsFilterAdminView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-vac-fit-adm-sec:spgvc').length > 0;
  }

  canViewStudentVacationsRequestsFilterStudentView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-vac-fit-std-sec:spgvc').length > 0;
  }

  canRejectStudentVacationRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'rej-std-vac:spgvc').length > 0;
  }
  canAcceptStudentVacationRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'acp-std-vac:spgvc').length > 0;
  }

  canAddStudentVacation() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-std-vac:spgvc').length > 0;
  }

  canStudentCancelStudentVacation() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'cnl-std-vac:spgvc').length > 0;
  }
  canStudentTerminateStudentVacation() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ter-std-vac:spgvc').length > 0;
  }

  canStudentViewAvailableProgramsForApplyVacationRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-ava-prg-f-vac:spgvc').length > 0;
  }
  // module 

  canViewStudentStatistics() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-stsc:stdsts').length > 0;
  }
  // module 

  canViewTeacherAppointmentFilterAdminView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tec-apo-fit-adm-sec:teapo').length > 0;
  }
  canViewTeacherAvailableTimes() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tec-ava-tms:teapo').length > 0;
  }

  canTeacherAddChangeAvailableTimeRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-tec-ava-tms:teapo').length > 0;
  }

  canTeacherCancelChangeAvailableTimeRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'cnl-tec-ava-tm-req:teapo').length > 0;
  }

  canRejectChangeAvailableTimeRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'rj-tec-ava-tm-req:teapo').length > 0;
  }
  canAcceptChangeAvailableTimeRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'acp-tec-ava-tm-req:teapo').length > 0;
  }
  canViewTeacherChangeAvailableTimeReqests() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tec-apo-req-apps:teapo').length > 0;
  }

  canViewTeacherCahngeAvailableTimeRequestByBatchId() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tec-ava-tm-b-bat:teapo').length > 0;
  }
  // module 

  canViewTeacherDropoutRequestAdminView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tch-drpo-req-adm-sec:tedrpo').length > 0;
  }

  canAcceptTeacherDropoutRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'acp-tch-drpo-req:tedrpo').length > 0;
  }

  canRejectTeacherDropoutRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'rej-tch-drpo-req:tedrpo').length > 0;
  }
  canAddTeacherDropoutRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-tch-drpo-req:tedrpo').length > 0;
  }

  canViewTeacherDropoutRequestFilterTeacherView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tch-drpo-req-fit-tec-sec:tedrpo').length > 0;
  }


  canTeacherCancelDropoutRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'cnl-tch-drpo-req:tedrpo').length > 0;
  }

  canTeacherViewAvailableProgsToApplyDropoutRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tch-ava-prg-drpo:tedrpo').length > 0;
  }
  // module 

  canTeacherEditProfile() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ed-tch-prof:tchsr').length > 0;
  }
  canTeacherViewProgfileDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tch-prof-det:tchsr').length > 0;
  }
  canViewTeacherSystemSubscriptionAdvancedFilter() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tch-sys-sub-adv-fit:tchsr').length > 0;
  }
  canAcceptTeacherSystemSubscriptionRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'acp-tch-sys-sub:tchsr').length > 0;
  }

  canTeacherUpdateProfileStatus() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'rej-tch-sys-sub:tchsr').length > 0;
  }
  canRejectTeacherSystemSubscriptionRequest() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ed-tch-prof-sta:tchsr').length > 0;
  }

  canDeleteTeacher() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-tch:tchsr').length > 0;
  }
  // module 
  canViewTeacherListAdminTeacherTab() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tchs:tecmg').length > 0;
  }
  canAddTeacherToSharedProgram() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-tch-shrd-prog:tecmg').length > 0;
  }

  canViewTeacherBatchDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tec-bat-det:tecmg').length > 0;
  }
  // module 

  canViewTeacherProgramSubscriptionFilterAdminView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tch-prg-sub-fit:tpgsu').length > 0;
  }
  canAcceptTeacherProgramSubscription() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'acc-tch-sub:tpgsu').length > 0;
  }
  canRejectTeacherProgramSubscription() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'rej-tch-prg-sub:tpgsu').length > 0;
  }
  canSubmitTeacherProgramSubsciption() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'sub-tch-prg-sub:tpgsu').length > 0;
  }
  canViewTeacherPorgramSubscription() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tch-prgs:tpgsu').length > 0;
  }
  canViewTeacherProgramSubscriptionFilterTeacherView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tchs-prgs-sub-fit-tec-sec:tpgsu').length > 0;
  }
  canViewTeacherProgramsForSubscription() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-prgs-f-tch-sub:tpgsu').length > 0;
  }
  // module 

  canViewStudentBatchStatisticsTeacherStatisticsView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-bat-stsc:tecsts').length > 0;
  }
  canVewStudentAgendaStatistics() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-std-agn-stsc:tecsts').length > 0;
  }
  canViewTeacherBatchStatisticsTeacherStatisticsView() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tch-stsc-bat:tecsts').length > 0;
  }
  canViewTeacherStatstics() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-tch-stscs:tecsts').length > 0;
  }
  // module 

  canEditUserDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ed-usr:usrsr').length > 0;
  }
  canViewUserProfileDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-usr-prf-det:usrsr').length > 0;
  }
  canDeleteUserProfile() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'del-usr:usrsr').length > 0;
  }
  canViewUserSimpleDetails() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-usr-det:usrsr').length > 0;
  }

  canUploadProfilePicture() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'upl-prf-pic:usrsr').length > 0;
  }


  canGetCountryCode() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'vw-cun-cd:usrsr').length > 0;
  }
  canActivateAndDeactivateUser() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'act-deac-usr:usrsr').length > 0;
  }
  canAddUserFromUserManagement() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-use-f-srmg:usrsr').length > 0;
  }
  canOrderQuestionBankCategories() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ord-qb-catgs:catg').length > 0;
  }

  canAddWalkthroughPage() {
    let scopes = this.getUserScopes();
    return scopes.filter(item => item === 'ad-wt-pg:wt').length > 0;
  }

  adminDashBoardMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-adb-hon-bord:adb').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-adb-stc:adb').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-adb-tot-ex-deg:adb').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-adb-tot-tas-deg:adb').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-adb-tas-con:adb').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-adb-tasm-con:adb').length > 0){
      return true; 
    } 
    else if (scopes.filter(item => item === 'vw-adb-mat-con-pro:adb').length > 0){
      return true; 
    }
    return false;
  }

  programsMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'ad-prog:prog').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'ed-prog:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'filt-nm-prog:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'adv-filt-prog:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'del-prog:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'pub-pau-prog:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ad-prog-dy-tsk:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'sv-prog-dy-tsk-det:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'cop-prog-dy-tsk:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'del-prog-dy-tsk:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-tsk-prog-dty-dy:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ed-ord-by-prog-dy-tsk:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ad-emfm-prog:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ed-em-prog:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'cpy-prog:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ad-prog-notf:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ed-prog-notf:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'del-prog-notf:prog"').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-prog-notfs:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'view-det-prog:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ass-cond-prog:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'lst-fv-mem-tsks-prog:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-ass-prog-cond:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ed-prog-cond-det:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'del-prog-cond:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-cond-no-ass-prog:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-prog-subs-det:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-shrd-progs:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-pub-no-del-progs:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-prog-mem-tsks-dy:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-prog-dys:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-all-progs:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'is-prog-cont-sard-tsk:prog').length > 0){
      return true; 
    }
    return false;
  }

  scientificMatrialMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'ad-scm:scm').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-scm-det:scm').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ed-scm:scm').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'del-scm:scm').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-scm-s:scm').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-scm-catg:scm').length > 0){
      return true; 
    }
    return false;
  }

  adminProgramVacationMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-std-vac-fit-adm-sec:spgvc').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'reject-student-program-vacation').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'acp-std-vac:spgvc').length > 0){
      return true; 
    }
    return false;
  }

  adminStudentMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-usr-not:pshnt').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-pub-no-del-progs:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-stds:stdmg').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-usr-prf-det:usrsr').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-adb-tot-ex-deg:adb').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-std-prog-bat-dys:stdmg').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-std-drpo-req-std-vfilt:stdrpo').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-std-prgs-sub-fit-std-sec:spgsu').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-std-vac-fit-std-sec:spgvc').length > 0){
      return true; 
    }
    return false;
  }

  adminTeatchMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-usr-not:pshnt').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'get-all-programs').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-tchs:tecmg').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-tch-prgs:tpgsu').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-tec-bat-det:tecmg').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-tch-drpo-req-fit-tec-sec:tedrpo').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-tchs-prgs-sub-fit-tec-sec:tpgsu').length > 0){
      return true; 
    }
    return false;
  }

  settingMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-usr-not:pshnt').length > 0)
    {
      return true;
    }
    return false;
  }

  adminMessgingMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-tch-sys-sub-adv-fit:tchsr').length > 0)
    {
      return true;
    }
    
    else if (scopes.filter(item => item === 'acp-tch-sys-sub:tchsr').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'rej-tch-sys-sub:tchsr').length > 0){
      return true; 
    }
    
    else if (scopes.filter(item => item === 'vw-std-sub-fit-adm-sec:spgsu').length > 0){
      return true; 
    }
    
    else if (scopes.filter(item => item === 'ac-std-prg-sub:spgsu').length > 0){
      return true; 
    }
    
    else if (scopes.filter(item => item === 'rej-std-prg-sub:spgsu').length > 0){
      return true; 
    }
    
    else if (scopes.filter(item => item === 'vw-tch-sys-sub-adv-fit:tchsr').length > 0){
      return true; 
    }
    
    else if (scopes.filter(item => item === 'vw-scp:scp').length > 0){
      return true; 
    }
    
    else if (scopes.filter(item => item === 'ad-scp:scp').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'del-scp-by-list:scp').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'del-scp:scp').length > 0){
      return true; 
    }
    return false;
  }

  adminQuestionBankMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'ad-qb-catg:catg').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-qb-catg-det:catg').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ed-qb-catg:catg').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'del-qb-catg:catg').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ord-qb-catgs:catg').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ad-qb-ques:ques').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-qb-ques-det:ques').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ed-qb-ques:ques').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'del-qb-ques:ques').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-qb-ques:ques').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ord-qb-ques:ques').length > 0){
      return true; 
    }
    return false;
  }

  teacherDashboardMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-tec-ava-tms:teapo').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'show-pub-fels:fels').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ad-tec-ava-tms:teapo').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ad-fels:fels').length > 0){
      return true; 
    }
    return false;
  }

  teacherWorkAgindaMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-tech-fr-rec-app-req:cls').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-tech-fr-rec-app-req:cls').length > 0){
      return true; 
    }
    return false;
  }

  teacherMyProgramMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-tch-prgs:tpgsu').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-tch-stsc-bat:tecsts').length > 0){
      return true; 
    }
    return false;
  }

  teacherAppoGroupMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-grp-exp-tech-mod:cls').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-usrs-ad-in-grp-exp:cls').length > 0){
      return true; 
    }
    
    else if (scopes.filter(item => item === 'ad-grEx:cls').length > 0){
      return true; 
    }
    
    else if (scopes.filter(item => item === 'ad-grp-exp:cls').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'acp-grp-exp-req:cls').length > 0){
      return true; 
    }
    
    else if (scopes.filter(item => item === 'rej-grp-exp-req:cls').length > 0){
      return true; 
    }
    
    else if (scopes.filter(item => item === 'vw-grp-exp-det:cls').length > 0){
      return true; 
    }
    return false;
  }

  teacherRequestMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-tchs-prgs-sub-fit-tec-sec:tpgsu').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-tch-drpo-req-fit-tec-sec:tedrpo').length > 0){
      return true; 
    }
    return false;
  }

  teacherProgForSubMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-prgs-f-tch-sub:tpgsu').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-prog-subs-det:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'sub-tch-prg-sub:tpgsu').length > 0){
      return true; 
    }
    return false;
  }

  // teacherChatMenuItem(){
  //   let scopes = this.getUserScopes();
  //   if(scopes.filter(item => item === '').length > 0)
  //   {
  //     return true;
  //   }
  //   return false;
  // }

  teacherQuestionBankMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-qb-catg-det:catg').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-qb-ques:ques').length > 0){
      return true; 
    }
    return false;
  }

  studDashBoardMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'show-pub-fels:fels').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-std-bat-no-com:cer').length > 0){
      return true; 
    }
    
    else if (scopes.filter(item => item === 'vw-exm-std-bat-no-com:cer').length > 0){
      return true; 
    }

    else if (scopes.filter(item => item === 'ad-fels:fels').length > 0){
      return true; 
    }
    return false;
  }

  studSardTecMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-std-prgs-strd:spgsu').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-ava-tech-fr-rec:cls').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-std-app-fr-rec:cls').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-ava-tech-fr-rec:cls').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ad-fr-rec-app:cls').length > 0){
      return true; 
    }
    return false;
  }

  studMyProgMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-std-prgs:spgsu').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-prog-bat-hon-brd:cer').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-std-stsc:stdsts').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-std-prog-det:stddwf').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'str-std-bat:stddwf').length > 0){
      return true; 
    }
    return false;
  }

  studScientificMatrialMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'filt-nm-prog:prog').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-scm-s:scm').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-scm-catg:scm').length > 0){
      return true; 
    }
    return false;
  }

  studVacationMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-std-vac-fit-std-sec:spgvc').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-ava-prog-lst-std-drpo:stdrpo').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-std-ava-prg-f-vac:spgvc').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ad-std-vac:spgvc').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'cnl-std-vac:spgvc').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ter-std-vac:spgvc').length > 0){
      return true; 
    }
    return false;
  }

 studExamResultMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-std-bat-com:cer').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-exm-std-bat-com:cer').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-prog-bat-hon-brd:cer').length > 0){
      return true; 
    }
    return false;
  }

  studPrintCertificateMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-std-bat-com:cer').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'pr-cr:cer').length > 0){
      return true; 
    }
    return false;
  }

  studRequstMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-std-prgs-sub-fit-std-sec:spgsu').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-std-drpo-req-std-vfilt:stdrpo').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ad-std-drpo-rqs:stdrpo').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-scp-det:scp').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'create-scientific-problem').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vw-prg-std-sub:spgsu').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'del-scp:scp').length > 0){
      return true; 
    }
    return false;
  }

  studProgForSubMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-prg-std-sub:spgsu').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-prog-subs-det:prog').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'vfy-prg-prd-con:spgsu').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'std-prg-com-sub:spgsu').length > 0){
      return true; 
    }
    return false;
  }

  studentGroupsMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-grp-exp-std-mod').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-grps-exp-no-bln-usr:cls').length > 0){
      return true; 
    }
    return false;
  }
studQuestionBankMenuItem(){
    let scopes = this.getUserScopes();
    if(scopes.filter(item => item === 'vw-qb-catg-det:catg').length > 0)
    {
      return true;
    }
    else if (scopes.filter(item => item === 'vw-qb-ques:ques').length > 0){
      return true; 
    }
    else if (scopes.filter(item => item === 'ad-scp:scp').length > 0){
      return true; 
    }
    return false;
  }
}



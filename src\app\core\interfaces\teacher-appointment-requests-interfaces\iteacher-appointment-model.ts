import {IDayModel} from "./iteacher-available-times";
import {BaseLookupModel} from "../../ng-model/base-lookup-model";

export interface ITeacherAppointmentModel {
  avatarLink?: string;
  id?: string;
  usrId?:string;
  requestDate?: Date;
  usrNameAr?: string;
  usrNameEn?: string;
  progName?: string;
  totalRows?: number;
  rejReason?: string;
  arNameBatch?: string;
  enNameBatch?: string;
  checked?:boolean;
  arProgBatchName?:string;
  enProgBatchName?:string;
  newFromTime?:string;
  newToTime?:string;
  oldDayAr?:string;
  oldDayEn?:string;
  newDayAr?:string;
  newDayEn?:string;
  oldFromTime?:string;
  oldToTime?:string;
}
export interface ITeachersAppointmentRequestsModel
    {
         avatarLink?: string;
         reqId?:string;
         usrId?:string;
         usrNameAr?:string;
         usrNameEn?:string;
         checked?:boolean;
         reqDate?:string;
         rejReason?:string | undefined;
         huffazId?:number;
         availableTimeRequests?: IAvailableTimeRequests[];
         teacherAvailableTimes?: IAvailableTimeRequests[];
         availableTimeRequestsListGroup?:IAvailableTimeGroupingModel[];
      teacherAvailableTimesListGroup?:IAvailableTimeGroupingModel[];
      dayName?:BaseLookupModel | undefined;
    }

export interface IAvailableTimeGroupingModel {
  availabilityDay?:number
  nameAr?:string;
  nameEn?:string;
  availableTimes?: IAvailableTimeRequests[];
}
    export interface IAvailableTimeRequests
    {
         from?:string;
         to?:string;
         dayNameAr?:string;
         dayNameEn?:string;
    }

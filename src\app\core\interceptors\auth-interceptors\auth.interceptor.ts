import { Injectable } from '@angular/core';
import {
  <PERSON>ttp<PERSON>equest,
  <PERSON>ttpH<PERSON>ler,
  HttpEvent,
  HttpInterceptor
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { IUser } from '../../interfaces/auth-interfaces/iuser-model';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  currentUser: IUser | undefined; 
  constructor() {}

  intercept(request: HttpRequest<unknown>, next: <PERSON>ttp<PERSON><PERSON><PERSON>): Observable<HttpEvent<unknown>> {
    this.currentUser = JSON.parse(localStorage.getItem('user') || '{}') as IUser;

    if (this.currentUser) {
      request = request.clone({
        headers: request.headers.set("Authorization", "Bearer " + this.currentUser.token),
      });
    }

    // if (!request.headers.has("Content-Type")) {
    //   request = request.clone({
    //     headers: request.headers.set("Content-Type", "application/json"),
    //   });
    // }

    request = request.clone({
      headers: request.headers.set("Accept", "application/json"),
    });

    request = request.clone({
      withCredentials: true
    });

    return next.
    handle(request);
  }
}

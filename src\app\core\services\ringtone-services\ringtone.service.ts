import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class RingtoneService {
  private audio = new Audio('assets/audios/ringtone.mp3');

  constructor() {}
  
  play() {
    this.audio.loop = true;
    this.audio.play();

    // this.audio.addEventListener('ended', () => {
    //   this.stop();
    // });

    setTimeout(() => {
      this.stop();
    }, 30000);
  }

  stop() {
    this.audio.pause();
    this.audio.currentTime = 0;
  }
}
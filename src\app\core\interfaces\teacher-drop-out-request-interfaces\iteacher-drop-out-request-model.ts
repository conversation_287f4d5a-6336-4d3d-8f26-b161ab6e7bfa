
export interface ITeacherDropOutRequestModel {
    id?: string;
    usrId?: string;
    no?: number;
    teacherNameAr?: string;
    teacherNameEn?: string;
    batchAr?: string;
    batchEn?: string;
    progName?: string;
    arProgBatName?: string;
    enProgBatName?: string;
    interviewiHijri?: string;
    interviewGregorian?: string;
    avatarLink?: string;
    checked?: boolean;
    reasonReject?: string;
    totalRows?: number;
    requestDate?: string;
    arTechStatusName?: string;
    enTechStatusName?: string;
    drpStat?: number;
    canCancel?: boolean;
    reason?: string;
    arStatName?: string;
    enStatName?: string;
    statNum?: number;


}

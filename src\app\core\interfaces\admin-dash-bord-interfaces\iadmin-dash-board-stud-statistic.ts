export interface IAdminDashBoardStudStatistic {
    countStudCompletedModel?: countStudCompletedModel;//الخاتمين 
    countStudProgVacationModel?: countStudProgVacationModel;// الطلاب المستأذنين
    countStudentDropOutModel?: countStudentDropOutModel;//الطلاب المنسحبين
    //الزائرين ملغيه 
    registStudCountModel?: registStudCountModel; //عدد الطلاب //المسجلين
    registStudFmalCountModel?: registStudFmalCountModel;
    registStudMalCountModel?: registStudMalCountModel;
    registTeatchCountModel?: registTeatchCountModel;//عدد المعلمين
    registTeatchFmalCountModel?: registTeatchFmalCountModel;
    registTeatchMalCountModel?: registTeatchMalCountModel;
    stNotCompProf?:number;
    stCompProf?:number;

}
export interface countStudCompletedModel {
    countStudCompleted?: number;
}
export interface countStudProgVacationModel {
    countStudProgVacation?: number;
}
export interface countStudentDropOutModel {
    countStudentDropOut?: number;
}
export interface registStudCountModel {
    registStudCount?: number;
}
export interface registStudFmalCountModel {
    registStudFmalCount?: number;
}
export interface registStudMalCountModel {
    registStudMalCount?: number;
}

export interface registTeatchCountModel {
    registTeatchCount?: number;
}

export interface registTeatchFmalCountModel {
    registTeatchFmalCount?: number;
}
export interface registTeatchMalCountModel {
    registTeatchMalCount?: number;
}

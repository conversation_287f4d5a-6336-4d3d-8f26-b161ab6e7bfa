import { IProgramDayTaskTasmea } from "../../programs-interfaces/program-day-tasks-interfaces/iprogram-day-task-tasmea";
import { IAvailableStudentResponse } from "../iavailable-student-response";
import { IProgGenericTskCallConnector } from "./iprog-generic-tsk-call-connector";

export interface IProgStuRecitationTskCallConnector extends IProgGenericTskCallConnector {
    availableStuInfo?:IAvailableStudentResponse;
    stuRecitationTskDetailsJsonModel?:IProgramDayTaskTasmea;
    isVideoMute ?: boolean ;
}
